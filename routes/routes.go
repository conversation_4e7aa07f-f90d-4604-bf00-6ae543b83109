package routes

import (
	"beacon/cloud/controller"
	"beacon/cloud/middleware"
	"log"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func Routes(r *gin.Engine) {
	// Initialize Casbin
	if _, err := middleware.InitCasbin(); err != nil {
		log.Fatalf("Failed to initialize Casbin: %v", err)
	}

	controller.Init()

	// CORS configuration
	r.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Authorization"},
		ExposeHeaders:    []string{"Content-Length", "Content-Type"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))
	// Public routes
	r.POST("/api/register", controller.Register)
	r.POST("/api/registercode", controller.SendRegisterCode)
	r.POST("/api/forgotcode", controller.ForgotPassword)
	r.POST("/api/login", controller.Login)
	r.POST("/api/resetpwd", controller.ResetPassword)
	r.POST("/api/location", controller.ReceiveDeviceLocation)
	r.GET("/api/bind/:token", controller.BindDeviceCallback)
	// 添加公共的peer allowed IPs路由
	r.POST("/api/wg/peer/allowed-ips/public", controller.UpdatePeerAllowedIPsPublic)

	// Routes requiring authentication
	api := r.Group("/api")
	api.Use(middleware.AuthMiddleware())
	api.Use(middleware.Authorize()) // Add Casbin authorization
	{
		// System info
		api.GET("/sys/info", controller.GetSystemInfo)

		// User operations
		userGroup := api.Group("/user")
		{
			userGroup.POST("/updatepwd", controller.UpdatePassword)
			userGroup.POST("/updateprofile", controller.UpdateProfile)
			userGroup.GET("/getusers", controller.GetUsers)
			userGroup.DELETE("/:id", controller.AdminDeleteUser)
			userGroup.GET("/info/:id", controller.GetUserProfile)
			userGroup.POST("/updateuser/:id", controller.AdminUpdateUser)
			userGroup.POST("/forceresetpwd/:email", controller.ForceResetPassword)
			userGroup.POST("/updateavatar", controller.UpdateAvatar)
		}

		// Enterprise management
		enterpriseGroup := api.Group("/enterprise")
		{
			enterpriseGroup.POST("/code", controller.GenerateCode)
			enterpriseGroup.GET("/codes", controller.ListCodes)
			enterpriseGroup.DELETE("/code/:code", controller.DeleteCode)
			enterpriseGroup.POST("/operator", controller.CreateOperator)
			enterpriseGroup.DELETE("/operator/:id/:enterprise_id", controller.DeleteOperator)
			enterpriseGroup.GET("/operators", controller.GetOperators)
		}

		// WireGuard operations
		wgGroup := api.Group("/wg")
		{
			wgGroup.POST("/start", controller.StartWgInterface)
			wgGroup.POST("/stop", controller.StopWgInterface)
			wgGroup.POST("/newdevice", controller.CreateWireguardDevice)
			wgGroup.POST("/newpeer", controller.NewWgDevicePeer)
			wgGroup.GET("/listdevices", controller.ListDevices)
			wgGroup.GET("/device", controller.GetWgDeviceStatus)
			wgGroup.POST("/devices", controller.GetWgDevicesStatus)
			wgGroup.POST("/device", controller.DeleteWgDevice)
			wgGroup.POST("/peer", controller.DeleteWgDevicePeer)
			wgGroup.GET("/qrcode", controller.GetWgPeerQRCode)
			wgGroup.GET("/status", controller.GetWgInterfaceStatus)
			wgGroup.GET("/traffic", controller.GetWgStats)
			wgGroup.POST("/bandwidth/limit", controller.SetWgBandwidthLimit)
			wgGroup.POST("/namespace/enable", controller.EnableNamespaceIsolation)
			wgGroup.POST("/namespace/disable", controller.DisableNamespaceIsolation)
			wgGroup.DELETE("/bandwidth/limit", controller.RemoveWgBandwidthLimit)
			wgGroup.GET("/bandwidth/limit", controller.GetWgBandwidthLimit)
			wgGroup.GET("/bandwidth/limits", controller.ListWgBandwidthLimits)
			wgGroup.POST("/bandwidth/restore", controller.RestoreWgBandwidthLimits)
			wgGroup.GET("/peer/allowed-ips", controller.GetPeerAllowedIPs)
			wgGroup.POST("/peer/allowed-ips", controller.SavePeerAllowedIPs)
		}

		// MQTT operations
		api.GET("/mqttinfo", controller.MqttInfo)
		mqttGroup := api.Group("/mqtt")
		{
			mqttGroup.POST("/topics", controller.CreateMqttTopic)
			mqttGroup.GET("/topics/messages", controller.GetMqttTopicMessages)
			mqttGroup.GET("/topics/history", controller.GetTopicHistoryMessages)
			mqttGroup.POST("/publish", controller.PublishMessage)
			mqttGroup.GET("/topics/ws", controller.BatchQueryLatestMessagesWS)
			mqttGroup.POST("/threshold", controller.CreateThresholdRule)
			mqttGroup.PUT("/threshold/:id", controller.UpdateThresholdRule)
			mqttGroup.GET("/threshold", controller.GetThresholdRule)
			mqttGroup.DELETE("/threshold/:id", controller.DeleteThresholdRule)
			mqttGroup.GET("/thresholds", controller.GetThresholdRules)
			mqttGroup.GET("/alarms", controller.GetTopicAlarms)
			mqttGroup.GET("/alarms/history", controller.GetAlarmHistory)
			mqttGroup.GET("/topics/available", controller.GetAvailableTopics)
		}

		// Device operations
		deviceGroup := api.Group("/device")
		{
			deviceGroup.GET("/bind", controller.BindDeviceQRCode)
			deviceGroup.GET("/list", controller.GetDeviceList)
			deviceGroup.POST("/register", controller.RegisterDevice)
			deviceGroup.POST("/import", controller.ImportDevice)
			deviceGroup.POST("/assign", controller.AssignDevice)
			deviceGroup.GET("/topicinfo", controller.GetTopicInfo)
			deviceGroup.DELETE("/topic", controller.DeleteTopicFromDevice)
			deviceGroup.DELETE("/:device_sn", controller.DeleteDevice)
			deviceGroup.POST("/unassign", controller.UnassignDevice)
			deviceGroup.POST("/update", controller.UpdateDevice)
			deviceGroup.GET("/ws/bind-status", controller.HandleBindStatusWS)
			deviceGroup.GET("/tracks", controller.GetDeviceTracks)
		}

		// Device Type operations
		typeGroup := api.Group("/types")
		{
			typeGroup.GET("", controller.GetDeviceTypes)
			typeGroup.POST("", controller.CreateDeviceType)
			typeGroup.DELETE("/:name", controller.DeleteDeviceType)
		}

		// SCADA operations
		scadaGroup := api.Group("/scada")
		{
			// 项目管理
			scadaGroup.POST("/projects", controller.SaveSCADAProject)
			scadaGroup.GET("/projects", controller.GetSCADAProjects)
			scadaGroup.GET("/projects/:id", controller.GetSCADAProject)
			scadaGroup.DELETE("/projects/:id", controller.DeleteSCADAProject)

			// 导入导出
			scadaGroup.GET("/projects/:id/export", controller.ExportSCADAProject)
			scadaGroup.POST("/projects/import", controller.ImportSCADAProject)

			// 克隆功能
			scadaGroup.POST("/projects/:id/clone", controller.CloneSCADAProject)
		}
	}
}
