# WireGuard Traffic Isolation Implementation

## Overview

This implementation adds traffic isolation between different WireGuard interfaces using Linux network namespaces. This solves the problem of routing conflicts when multiple WireGuard interfaces use overlapping IP ranges (AllowedIPs).

## Key Features

1. **Network Namespace Isolation**: Each WireGuard interface can run in its own network namespace
2. **Backward Compatibility**: Existing interfaces continue to work without namespace isolation
3. **Dynamic Control**: Enable/disable namespace isolation for existing interfaces via API
4. **Automatic Cleanup**: Namespaces are automatically cleaned up when interfaces are deleted

## Architecture Changes

### New Components

1. **NamespaceManager** (`service/wireguardmgr/namespace.go`)
   - Manages network namespace creation, deletion, and operations
   - Provides utilities for executing commands within namespaces

2. **Enhanced Device Model** (`service/wireguardmgr/model.go`)
   - Added `Namespace` field to track namespace association

3. **Namespace-Aware Interface Management** (`service/wireguardmgr/wghelper.go`)
   - `StartInterfaceInNamespace()` - Starts interfaces within namespaces
   - `StopInterfaceInNamespace()` - Stops interfaces within namespaces
   - `setupNamespaceNetworking()` - Configures networking within namespaces

4. **Management Functions** (`service/wireguardmgr/manager.go`)
   - `EnableNamespaceIsolation()` - Enables isolation for existing devices
   - `DisableNamespaceIsolation()` - Disables isolation for existing devices

5. **API Endpoints** (`controller/wgController.go`)
   - `POST /api/wg/namespace/enable` - Enable namespace isolation
   - `POST /api/wg/namespace/disable` - Disable namespace isolation

## Usage

### Creating New Devices with Namespace Isolation

New devices created after this implementation will automatically have namespace isolation enabled. The namespace name is generated using the pattern: `wg-{deviceName}-{enterpriseID}`

### Enabling Namespace Isolation for Existing Devices

```bash
# Enable namespace isolation
curl -X POST "http://your-server/api/wg/namespace/enable?device_name=wg1&enterprise_id=your_enterprise_id"

# Disable namespace isolation
curl -X POST "http://your-server/api/wg/namespace/disable?device_name=wg1&enterprise_id=your_enterprise_id"
```

### Configuration Differences

#### Without Namespace Isolation (Traditional)
```ini
[Interface]
PrivateKey = ...
Address = ********/24
ListenPort = 51820
MTU=1420
PostUp = iptables -t nat -A POSTROUTING -s ********/24 -o eth0 -j MASQUERADE; ...
PostDown = iptables -t nat -D POSTROUTING -s ********/24 -o eth0 -j MASQUERADE; ...
```

#### With Namespace Isolation
```ini
[Interface]
PrivateKey = ...
Address = ********/24
ListenPort = 51820
MTU=1420
```

The namespace management is handled programmatically rather than through PreUp/PostUp commands.

## Benefits

1. **Routing Conflict Resolution**: Multiple interfaces can use identical AllowedIPs without conflicts
2. **Traffic Isolation**: Traffic from different interfaces cannot interfere with each other
3. **Security Enhancement**: Each interface operates in its own isolated network environment
4. **Scalability**: Supports unlimited number of interfaces with overlapping IP ranges

## Technical Details

### Namespace Naming Convention
Namespaces are named using the pattern: `wg-{deviceName}-{enterpriseID[:8]}`

### Network Isolation
- Each namespace has its own routing table
- iptables rules are applied within the namespace scope
- Interfaces are moved to their respective namespaces after creation

### Cleanup Process
- When a device is deleted, its namespace is automatically cleaned up
- All processes running in the namespace are terminated
- The namespace is removed from the system

## Troubleshooting

### Check Namespace Status
```bash
# List all namespaces
ip netns list

# Check interface in namespace
ip netns exec wg-device1-******** ip addr show

# Check routing in namespace
ip netns exec wg-device1-******** ip route show
```

### Manual Cleanup
```bash
# If automatic cleanup fails, manually remove namespace
ip netns delete wg-device1-********
```

### Logs
Check application logs for namespace-related operations:
```bash
# Look for namespace creation/deletion messages
grep -i namespace /var/log/your-app.log
```

## Migration Guide

### For Existing Deployments

1. **Backup Configuration**: Backup existing WireGuard configurations
2. **Update Application**: Deploy the new version with namespace support
3. **Enable Isolation**: Use the API to enable namespace isolation for existing devices
4. **Verify Operation**: Test that interfaces work correctly with isolation enabled

### Rollback Procedure

If issues occur, you can disable namespace isolation:
```bash
curl -X POST "http://your-server/api/wg/namespace/disable?device_name=wg1&enterprise_id=your_enterprise_id"
```

This will revert the interface to traditional operation mode.

## Requirements

- Linux kernel with network namespace support
- `ip` command (iproute2 package)
- `iptables` command
- Root privileges for namespace operations

## Limitations

1. Namespace operations require root privileges
2. Some monitoring tools may need updates to work with namespaced interfaces
3. Cross-namespace communication requires explicit routing configuration

## Future Enhancements

1. **Cross-Namespace Routing**: Allow controlled communication between namespaces
2. **Resource Limits**: Implement per-namespace resource limits
3. **Monitoring Integration**: Enhanced monitoring for namespaced interfaces
4. **GUI Management**: Web interface for namespace management
