#!/bin/bash

# Test script for WireGuard Traffic Isolation
# This script demonstrates how to test the namespace isolation functionality

set -e

# Configuration
SERVER_URL="http://localhost:8080"
ENTERPRISE_ID="test-enterprise-123"
DEVICE1_NAME="wg-test1"
DEVICE2_NAME="wg-test2"

echo "=== WireGuard Traffic Isolation Test ==="
echo "Server URL: $SERVER_URL"
echo "Enterprise ID: $ENTERPRISE_ID"
echo

# Function to make API calls
api_call() {
    local method=$1
    local endpoint=$2
    local data=$3
    
    echo "Making $method request to $endpoint"
    if [ -n "$data" ]; then
        curl -s -X $method "$SERVER_URL$endpoint" -H "Content-Type: application/json" -d "$data" | jq .
    else
        curl -s -X $method "$SERVER_URL$endpoint" | jq .
    fi
    echo
}

# Function to check namespace existence
check_namespace() {
    local namespace=$1
    echo "Checking if namespace '$namespace' exists:"
    if ip netns list | grep -q "$namespace"; then
        echo "✓ Namespace '$namespace' exists"
        echo "Interfaces in namespace:"
        ip netns exec "$namespace" ip link show 2>/dev/null || echo "  No interfaces or namespace not accessible"
    else
        echo "✗ Namespace '$namespace' does not exist"
    fi
    echo
}

# Function to create a test device
create_device() {
    local device_name=$1
    echo "Creating device: $device_name"
    api_call POST "/api/wg/newdevice" "{\"device_name\":\"$device_name\",\"enterprise_id\":\"$ENTERPRISE_ID\"}"
}

# Function to enable namespace isolation
enable_isolation() {
    local device_name=$1
    echo "Enabling namespace isolation for: $device_name"
    api_call POST "/api/wg/namespace/enable?device_name=$device_name&enterprise_id=$ENTERPRISE_ID"
}

# Function to disable namespace isolation
disable_isolation() {
    local device_name=$1
    echo "Disabling namespace isolation for: $device_name"
    api_call POST "/api/wg/namespace/disable?device_name=$device_name&enterprise_id=$ENTERPRISE_ID"
}

# Function to delete a device
delete_device() {
    local device_name=$1
    echo "Deleting device: $device_name"
    api_call POST "/api/wg/device" "{\"device_name\":\"$device_name\",\"enterprise_id\":\"$ENTERPRISE_ID\"}"
}

# Function to list devices
list_devices() {
    echo "Listing all devices:"
    api_call GET "/api/wg/listdevices?enterprise_id=$ENTERPRISE_ID"
}

# Function to show namespace routing
show_namespace_routing() {
    local namespace=$1
    echo "Routing table for namespace '$namespace':"
    if ip netns list | grep -q "$namespace"; then
        ip netns exec "$namespace" ip route show 2>/dev/null || echo "  Cannot access routing table"
    else
        echo "  Namespace does not exist"
    fi
    echo
}

# Main test sequence
echo "Starting test sequence..."
echo

# Step 1: Create two test devices
echo "=== Step 1: Creating test devices ==="
create_device "$DEVICE1_NAME"
create_device "$DEVICE2_NAME"

# Step 2: List devices to verify creation
echo "=== Step 2: Listing devices ==="
list_devices

# Step 3: Check initial namespace state
echo "=== Step 3: Initial namespace state ==="
echo "Current namespaces:"
ip netns list
echo

# Step 4: Enable namespace isolation for device 1
echo "=== Step 4: Enable namespace isolation for $DEVICE1_NAME ==="
enable_isolation "$DEVICE1_NAME"

# Step 5: Check namespace creation
echo "=== Step 5: Check namespace creation ==="
NAMESPACE1="wg-$DEVICE1_NAME-${ENTERPRISE_ID:0:8}"
check_namespace "$NAMESPACE1"
show_namespace_routing "$NAMESPACE1"

# Step 6: Enable namespace isolation for device 2
echo "=== Step 6: Enable namespace isolation for $DEVICE2_NAME ==="
enable_isolation "$DEVICE2_NAME"

# Step 7: Check both namespaces
echo "=== Step 7: Check both namespaces ==="
NAMESPACE2="wg-$DEVICE2_NAME-${ENTERPRISE_ID:0:8}"
check_namespace "$NAMESPACE1"
check_namespace "$NAMESPACE2"

# Step 8: Show all namespaces
echo "=== Step 8: All namespaces ==="
echo "Current namespaces:"
ip netns list
echo

# Step 9: Test disabling isolation
echo "=== Step 9: Disable namespace isolation for $DEVICE1_NAME ==="
disable_isolation "$DEVICE1_NAME"

# Step 10: Check namespace cleanup
echo "=== Step 10: Check namespace cleanup ==="
check_namespace "$NAMESPACE1"

# Step 11: Cleanup - delete devices
echo "=== Step 11: Cleanup - delete devices ==="
delete_device "$DEVICE1_NAME"
delete_device "$DEVICE2_NAME"

# Step 12: Final namespace check
echo "=== Step 12: Final namespace check ==="
echo "Remaining namespaces:"
ip netns list
echo

echo "=== Test completed ==="
echo "If you see any errors above, please check the server logs for more details."
echo "To manually clean up any remaining namespaces, use: ip netns delete <namespace-name>"
