package wireguardmgr

import (
	"fmt"
	"log"
	"os/exec"
	"strings"
)

// NamespaceManager handles network namespace operations for WireGuard traffic isolation
type NamespaceManager struct{}

// NewNamespaceManager creates a new namespace manager
func NewNamespaceManager() *NamespaceManager {
	return &NamespaceManager{}
}

// CreateNamespace creates a new network namespace for a WireGuard interface
func (nm *NamespaceManager) CreateNamespace(namespaceName string) error {
	// Check if namespace already exists
	if nm.NamespaceExists(namespaceName) {
		log.Printf("Namespace %s already exists", namespaceName)
		return nil
	}

	// Create the network namespace
	cmd := exec.Command("ip", "netns", "add", namespaceName)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to create namespace %s: %v, output: %s", namespaceName, err, output)
	}

	// Enable loopback interface in the namespace
	cmd = exec.Command("ip", "netns", "exec", namespaceName, "ip", "link", "set", "lo", "up")
	output, err = cmd.CombinedOutput()
	if err != nil {
		// Cleanup on failure
		nm.DeleteNamespace(namespaceName)
		return fmt.Errorf("failed to enable loopback in namespace %s: %v, output: %s", namespaceName, err, output)
	}

	log.Printf("Successfully created namespace %s", namespaceName)
	return nil
}

// DeleteNamespace removes a network namespace
func (nm *NamespaceManager) DeleteNamespace(namespaceName string) error {
	if !nm.NamespaceExists(namespaceName) {
		log.Printf("Namespace %s does not exist", namespaceName)
		return nil
	}

	cmd := exec.Command("ip", "netns", "delete", namespaceName)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to delete namespace %s: %v, output: %s", namespaceName, err, output)
	}

	log.Printf("Successfully deleted namespace %s", namespaceName)
	return nil
}

// NamespaceExists checks if a network namespace exists
func (nm *NamespaceManager) NamespaceExists(namespaceName string) bool {
	cmd := exec.Command("ip", "netns", "list")
	output, err := cmd.CombinedOutput()
	if err != nil {
		log.Printf("Failed to list namespaces: %v", err)
		return false
	}

	namespaces := strings.Split(string(output), "\n")
	for _, ns := range namespaces {
		// Handle both "namespace" and "namespace (id: X)" formats
		nsName := strings.Fields(ns)
		if len(nsName) > 0 && nsName[0] == namespaceName {
			return true
		}
	}
	return false
}

// ExecInNamespace executes a command in the specified namespace
func (nm *NamespaceManager) ExecInNamespace(namespaceName string, command []string) error {
	if !nm.NamespaceExists(namespaceName) {
		return fmt.Errorf("namespace %s does not exist", namespaceName)
	}

	// Prepend "ip netns exec namespaceName" to the command
	fullCmd := append([]string{"ip", "netns", "exec", namespaceName}, command...)
	cmd := exec.Command(fullCmd[0], fullCmd[1:]...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to execute command in namespace %s: %v, output: %s", namespaceName, err, output)
	}

	return nil
}

// ExecInNamespaceWithOutput executes a command in the specified namespace and returns output
func (nm *NamespaceManager) ExecInNamespaceWithOutput(namespaceName string, command []string) (string, error) {
	if !nm.NamespaceExists(namespaceName) {
		return "", fmt.Errorf("namespace %s does not exist", namespaceName)
	}

	// Prepend "ip netns exec namespaceName" to the command
	fullCmd := append([]string{"ip", "netns", "exec", namespaceName}, command...)
	cmd := exec.Command(fullCmd[0], fullCmd[1:]...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return string(output), fmt.Errorf("failed to execute command in namespace %s: %v", namespaceName, err)
	}

	return string(output), nil
}

// SetupNamespaceRouting configures routing for a namespace to use a specific interface
func (nm *NamespaceManager) SetupNamespaceRouting(namespaceName, interfaceName, gatewayIP string) error {
	// Add default route through the WireGuard interface
	err := nm.ExecInNamespace(namespaceName, []string{
		"ip", "route", "add", "default", "dev", interfaceName,
	})
	if err != nil {
		return fmt.Errorf("failed to add default route in namespace %s: %v", namespaceName, err)
	}

	log.Printf("Successfully configured routing for namespace %s", namespaceName)
	return nil
}

// MoveInterfaceToNamespace moves a network interface to a specific namespace
func (nm *NamespaceManager) MoveInterfaceToNamespace(interfaceName, namespaceName string) error {
	if !nm.NamespaceExists(namespaceName) {
		return fmt.Errorf("namespace %s does not exist", namespaceName)
	}

	cmd := exec.Command("ip", "link", "set", interfaceName, "netns", namespaceName)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to move interface %s to namespace %s: %v, output: %s", 
			interfaceName, namespaceName, err, output)
	}

	log.Printf("Successfully moved interface %s to namespace %s", interfaceName, namespaceName)
	return nil
}

// BringUpInterfaceInNamespace brings up an interface within a namespace
func (nm *NamespaceManager) BringUpInterfaceInNamespace(namespaceName, interfaceName, ipAddress string) error {
	// Set IP address
	err := nm.ExecInNamespace(namespaceName, []string{
		"ip", "addr", "add", ipAddress, "dev", interfaceName,
	})
	if err != nil {
		return fmt.Errorf("failed to set IP address for interface %s in namespace %s: %v", 
			interfaceName, namespaceName, err)
	}

	// Bring up the interface
	err = nm.ExecInNamespace(namespaceName, []string{
		"ip", "link", "set", interfaceName, "up",
	})
	if err != nil {
		return fmt.Errorf("failed to bring up interface %s in namespace %s: %v", 
			interfaceName, namespaceName, err)
	}

	log.Printf("Successfully brought up interface %s in namespace %s", interfaceName, namespaceName)
	return nil
}

// GenerateNamespaceName generates a unique namespace name for a WireGuard interface
func GenerateNamespaceName(deviceName, enterpriseID string) string {
	// Use a combination of device name and enterprise ID to ensure uniqueness
	// Limit length and use safe characters for namespace names
	return fmt.Sprintf("wg-%s-%s", deviceName, enterpriseID[:8])
}

// CleanupNamespaceResources performs cleanup of namespace-related resources
func (nm *NamespaceManager) CleanupNamespaceResources(namespaceName string) error {
	if !nm.NamespaceExists(namespaceName) {
		return nil
	}

	// Kill any processes running in the namespace
	cmd := exec.Command("ip", "netns", "pids", namespaceName)
	output, err := cmd.CombinedOutput()
	if err == nil && len(strings.TrimSpace(string(output))) > 0 {
		pids := strings.Fields(string(output))
		for _, pid := range pids {
			if pid != "" {
				exec.Command("kill", "-9", pid).Run()
			}
		}
	}

	// Delete the namespace
	return nm.DeleteNamespace(namespaceName)
}
